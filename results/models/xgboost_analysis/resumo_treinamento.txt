RESUMO DO TREINAMENTO XGBOOST MULTICLASSE - SINAIS DE TRADING
======================================================================

Data de treinamento: 2025-07-09 11:22:31

MODELO MULTICLASSE:
  • Tipo: XGBoost Multiclasse
  • Classes: 0=Sem Ação, 1=Compra, 2=Venda
  • Função de perda: Cross-entropy (mlogloss)
  • Acurácia geral: 0.503

CONFIGURAÇÕES UTILIZADAS:
  • Período de dados: 5y
  • Horizonte de sinais: 1 dias
  • Lags OHLC: 5
  • Janela volatilidade: 20
  • Multiplicador spread: 0.5

FEATURES UTILIZADAS (8):
   1. Media_OHLC_Lag_1
   2. Media_OHLC_Lag_2
   3. Media_OHLC_Lag_3
   4. Media_OHLC_Lag_4
   5. Media_OHLC_Lag_5
   6. Volume
   7. Spread
   8. Volatilidade

RESULTADOS DO MODELO:
  • Acurácia Geral: 0.503
  • Distribuição das Predições:
    - Sem Ação: 2 (0.0%)
    - Compra: 5214 (56.6%)
    - Venda: 3993 (43.4%)

DEFINIÇÃO DOS SINAIS:
  • Sinal de Compra: Média OHLC atual < Média OHLC 1 dias à frente
  • Sinal de Venda: Média OHLC atual > Média OHLC 1 dias à frente
  • Sem Ação: Casos onde não há sinal de compra nem venda

PARÂMETROS DO XGBOOST:
  • n_estimators: 100
  • max_depth: 6
  • learning_rate: 0.1
  • random_state: 42
  • eval_metric: logloss
  • objective: multi:softprob (adicionado automaticamente)
  • num_class: 3 (adicionado automaticamente)
  • eval_metric: mlogloss (adicionado automaticamente)
