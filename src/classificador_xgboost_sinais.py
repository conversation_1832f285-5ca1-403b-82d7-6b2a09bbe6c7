#!/usr/bin/env python3
"""
Script para classificação de sinais de compra e venda usando XGBoost
Baseado na média OHLC das ações diversificadas com sinais futuros
Usa features: média OHLC (10 dias passados), volume, spread e volatilidade
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta
import xgboost as xgb
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.preprocessing import StandardScaler
import seaborn as sns
import pickle
import warnings
warnings.filterwarnings('ignore')

# Adicionar o diretório src ao path para importar functions e config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from functions import edge_rolling
from config_loader import config, setup_environment

def carregar_acoes_diversificadas():
    """
    Carrega todas as ações do arquivo CSV de diversificação usando configuração
    """
    try:
        # Usar configuração ao invés de hardcode
        file_paths = config.get_file_paths()
        csv_path = file_paths['acoes_diversificacao']
        df = pd.read_csv(csv_path)

        # Pegar todas as ações (excluindo linhas vazias)
        acoes = []
        for _, row in df.iterrows():
            if pd.notna(row['Ticker']) and row['Ticker'].strip():
                ticker = row['Ticker'] + '.SA'
                nome = row['Nome']
                acoes.append((ticker, nome))

        print(f"📋 Carregadas {len(acoes)} ações diversificadas do arquivo: {csv_path}")
        return acoes

    except Exception as e:
        print(f"❌ Erro ao carregar arquivo CSV: {e}")
        return []

def baixar_dados_acao(ticker, nome):
    """
    Baixa dados históricos de uma ação usando configuração
    """
    try:
        # Usar configuração para período de dados do XGBoost
        periodo = config.get('xgboost.data_period')
        print(f"     📊 Baixando dados de {ticker} ({nome}) - período: {periodo}")

        # Baixar dados históricos
        dados = yf.download(ticker, period=periodo, progress=False)

        if dados.empty:
            print(f"     ❌ Nenhum dado encontrado para {ticker}")
            return None

        # Corrigir MultiIndex se necessário
        if isinstance(dados.columns, pd.MultiIndex):
            dados.columns = dados.columns.droplevel(1)

        # Verificar se tem dados suficientes
        if len(dados) < 100:
            print(f"     ⚠️ Poucos dados para {ticker}: {len(dados)} dias")
            return None

        return dados

    except Exception as e:
        print(f"     ❌ Erro ao baixar dados de {ticker}: {e}")
        return None

def corrigir_valores_zero_ultimo_dia(dados):
    """
    Corrige valores zero no último dia substituindo pelo penúltimo dia
    """
    if len(dados) < 2:
        return dados

    # Colunas OHLC para verificar
    colunas_ohlc = ['Open', 'High', 'Low', 'Close']

    # Verificar e corrigir valores zero no último dia
    for coluna in colunas_ohlc:
        if coluna in dados.columns:
            # Obter o último valor como escalar
            ultimo_valor = dados[coluna].iloc[-1]
            if hasattr(ultimo_valor, 'item'):
                ultimo_valor = ultimo_valor.item()

            # Verificar se é zero ou NaN
            if pd.isna(ultimo_valor) or ultimo_valor == 0:
                # Substituir pelo valor do penúltimo dia
                penultimo_valor = dados[coluna].iloc[-2]
                if hasattr(penultimo_valor, 'item'):
                    penultimo_valor = penultimo_valor.item()

                dados.loc[dados.index[-1], coluna] = penultimo_valor
                print(f"     🔧 Corrigido valor zero em {coluna}: {ultimo_valor} → {penultimo_valor}")

    return dados

def calcular_features_e_sinais(dados, ticker=None, tickers_carteira=None):
    """
    Calcula features e sinais de compra/venda baseados na média OHLC usando configuração
    Sinais são gerados para todas as ações (para comparação justa entre métodos)
    """
    # Corrigir valores zero no último dia
    dados = corrigir_valores_zero_ultimo_dia(dados)

    # Calcular média OHLC
    dados['Media_OHLC'] = (dados['Open'] + dados['Close'] + dados['Low'] + dados['High']) / 4

    # Usar configurações do XGBoost
    volatility_window = config.get('xgboost.features.volatility_window')
    signal_horizon = config.get('xgboost.signal_horizon')
    ohlc_lags = config.get('xgboost.features.ohlc_lags')

    # Calcular volatilidade usando janela configurada
    returns = dados['Media_OHLC'].pct_change()
    dados['Volatilidade'] = returns.rolling(window=volatility_window).std() * 100

    # Calcular spread bid-ask usando a função edge_rolling
    try:
        spread_series = edge_rolling(dados, window=volatility_window)
        dados['Spread'] = spread_series * 100  # Converter para percentual
    except Exception as e:
        print(f"     ⚠️ Erro ao calcular spread para {ticker}: {e}")
        # Fallback: usar uma estimativa simples baseada na volatilidade intraday
        high_low_spread = ((dados['High'] - dados['Low']) / dados['Media_OHLC']) * 100
        dados['Spread'] = high_low_spread.rolling(window=volatility_window).mean()

    # Preencher NaN
    dados['Spread'] = dados['Spread'].fillna(dados['Spread'].mean())
    dados['Volatilidade'] = dados['Volatilidade'].fillna(dados['Volatilidade'].mean())

    # Criar sinais futuros usando horizonte configurado
    dados['Media_OHLC_Futura'] = dados['Media_OHLC'].shift(-signal_horizon)

    # Sinal de compra: média OHLC atual < média OHLC futura (para todas as ações)
    dados['Sinal_Compra'] = (dados['Media_OHLC'] < dados['Media_OHLC_Futura']).astype(int)

    # Sinal de venda: média OHLC atual > média OHLC futura (para todas as ações)
    dados['Sinal_Venda'] = (dados['Media_OHLC'] > dados['Media_OHLC_Futura']).astype(int)

    # Criar features de média OHLC passada usando configuração
    for i in range(1, ohlc_lags + 1):
        dados[f'Media_OHLC_Lag_{i}'] = dados['Media_OHLC'].shift(i)

    # Remover linhas com NaN (início e fim da série)
    dados = dados.dropna()

    return dados

def preparar_dataset(acoes_dados):
    """
    Prepara dataset combinado de todas as ações para treinamento
    Preserva o índice de data para divisão temporal
    Cria target multiclasse: 0=Sem ação, 1=Compra, 2=Venda
    """
    datasets = []

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 50:
            # Adicionar coluna do ticker para identificação
            dados_copy = dados.copy()
            dados_copy['Ticker'] = ticker
            # Resetar índice para preservar as datas como coluna
            dados_copy = dados_copy.reset_index()
            datasets.append(dados_copy)

    if not datasets:
        print("❌ Nenhum dataset válido encontrado")
        return None, None, None, None

    # Combinar todos os datasets preservando as datas
    dataset_completo = pd.concat(datasets, ignore_index=True)

    # Converter coluna Date para datetime se necessário
    if 'Date' in dataset_completo.columns:
        dataset_completo['Date'] = pd.to_datetime(dataset_completo['Date'])
    elif dataset_completo.index.name == 'Date' or isinstance(dataset_completo.index, pd.DatetimeIndex):
        dataset_completo = dataset_completo.reset_index()
        dataset_completo['Date'] = pd.to_datetime(dataset_completo['Date'])

    # Features: média OHLC passada (configurável), volume, spread, volatilidade
    ohlc_lags = config.get('xgboost.features.ohlc_lags')
    feature_cols = [f'Media_OHLC_Lag_{i}' for i in range(1, ohlc_lags + 1)] + ['Volume', 'Spread', 'Volatilidade']

    # Verificar se todas as colunas existem
    colunas_existentes = [col for col in feature_cols if col in dataset_completo.columns]
    if len(colunas_existentes) != len(feature_cols):
        print(f"⚠️ Algumas features não encontradas. Disponíveis: {len(colunas_existentes)}/{len(feature_cols)}")
        feature_cols = colunas_existentes

    X = dataset_completo[feature_cols]

    # Criar target multiclasse: 0=Sem ação, 1=Compra, 2=Venda
    # Prioridade: se ambos sinais são 1, manter como compra (1)
    y_multiclass = np.zeros(len(dataset_completo), dtype=int)  # Default: sem ação

    # Aplicar sinais de compra (prioridade)
    compra_mask = dataset_completo['Sinal_Compra'] == 1
    y_multiclass[compra_mask] = 1

    # Aplicar sinais de venda (apenas onde não há sinal de compra)
    venda_mask = (dataset_completo['Sinal_Venda'] == 1) & (dataset_completo['Sinal_Compra'] == 0)
    y_multiclass[venda_mask] = 2

    # Estatísticas das classes
    unique, counts = np.unique(y_multiclass, return_counts=True)
    class_stats = dict(zip(unique, counts))

    print(f"📊 Dataset preparado (modelo multiclasse):")
    print(f"   • Total de amostras: {len(X)}")
    print(f"   • Features: {len(feature_cols)}")
    print(f"   • Colunas de features: {feature_cols}")
    print(f"   • Classe 0 (Sem ação): {class_stats.get(0, 0)} ({class_stats.get(0, 0)/len(y_multiclass)*100:.1f}%)")
    print(f"   • Classe 1 (Compra): {class_stats.get(1, 0)} ({class_stats.get(1, 0)/len(y_multiclass)*100:.1f}%)")
    print(f"   • Classe 2 (Venda): {class_stats.get(2, 0)} ({class_stats.get(2, 0)/len(y_multiclass)*100:.1f}%)")

    return X, y_multiclass, feature_cols, dataset_completo

def dividir_dados_temporal(dataset_completo, feature_cols, y_multiclass):
    """
    Divide os dados temporalmente: primeiros anos para treino, último ano para teste
    Trabalha com target multiclasse
    """
    if 'Date' not in dataset_completo.columns:
        print("❌ Coluna 'Date' não encontrada. Não é possível fazer divisão temporal.")
        return None, None, None, None

    # Ordenar por data
    dataset_ordenado = dataset_completo.sort_values('Date').copy()
    y_ordenado = y_multiclass[dataset_ordenado.index]

    # Encontrar data de corte (últimos 12 meses para teste)
    data_max = dataset_ordenado['Date'].max()
    data_corte = data_max - pd.DateOffset(years=1)

    print(f"📅 Divisão temporal dos dados:")
    print(f"   • Data máxima: {data_max.strftime('%Y-%m-%d')}")
    print(f"   • Data de corte: {data_corte.strftime('%Y-%m-%d')}")

    # Dividir dados
    mask_treino = dataset_ordenado['Date'] <= data_corte
    mask_teste = dataset_ordenado['Date'] > data_corte

    dados_treino = dataset_ordenado[mask_treino]
    dados_teste = dataset_ordenado[mask_teste]

    print(f"   • Dados de treino: {len(dados_treino)} registros ({dados_treino['Date'].min().strftime('%Y-%m-%d')} a {dados_treino['Date'].max().strftime('%Y-%m-%d')})")
    print(f"   • Dados de teste: {len(dados_teste)} registros ({dados_teste['Date'].min().strftime('%Y-%m-%d')} a {dados_teste['Date'].max().strftime('%Y-%m-%d')})")

    if len(dados_treino) == 0 or len(dados_teste) == 0:
        print("❌ Divisão temporal resultou em conjunto vazio")
        return None, None, None, None

    # Extrair features e targets
    X_train = dados_treino[feature_cols]
    X_test = dados_teste[feature_cols]
    y_train = y_ordenado[mask_treino]
    y_test = y_ordenado[mask_teste]

    return X_train, X_test, y_train, y_test

def treinar_classificador_multiclasse(X, y_multiclass, feature_cols, dataset_completo=None):
    """
    Treina um único classificador XGBoost multiclasse para sinais de trading
    Classes: 0=Sem ação, 1=Compra, 2=Venda
    Usa divisão temporal: primeiros 4 anos para treino, último ano para teste
    """
    # Usar configurações do XGBoost
    use_scaler = config.get('xgboost.use_standard_scaler')
    model_params = config.get('xgboost.model_params').copy()

    # Configurar para classificação multiclasse
    model_params['objective'] = 'multi:softprob'
    model_params['num_class'] = 3
    model_params['eval_metric'] = 'mlogloss'  # Cross-entropy loss

    # Fazer divisão temporal se dataset_completo estiver disponível
    if dataset_completo is not None and 'Date' in dataset_completo.columns:
        print("\n📅 Usando divisão temporal dos dados (primeiros 4 anos = treino, último ano = teste)")
        X_train, X_test, y_train, y_test = dividir_dados_temporal(
            dataset_completo, feature_cols, y_multiclass
        )

        if X_train is None:
            print("❌ Erro na divisão temporal. Usando divisão aleatória como fallback.")
            # Fallback para divisão aleatória
            test_size = config.get('xgboost.test_size')
            X_train, X_test, y_train, y_test = train_test_split(
                X, y_multiclass, test_size=test_size, random_state=model_params['random_state'],
                stratify=y_multiclass
            )
    else:
        print("\n⚠️ Dataset completo não disponível. Usando divisão aleatória.")
        # Fallback para divisão aleatória
        test_size = config.get('xgboost.test_size')
        X_train, X_test, y_train, y_test = train_test_split(
            X, y_multiclass, test_size=test_size, random_state=model_params['random_state'],
            stratify=y_multiclass
        )

    # Normalizar features se configurado
    if use_scaler:
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        X_train_scaled = pd.DataFrame(X_train_scaled, columns=feature_cols)
        X_test_scaled = pd.DataFrame(X_test_scaled, columns=feature_cols)
    else:
        scaler = None
        X_train_scaled = X_train
        X_test_scaled = X_test

    # Treinar classificador multiclasse
    print("\n🚀 Treinando classificador multiclasse XGBoost...")
    print(f"   • Classes: 0=Sem ação, 1=Compra, 2=Venda")
    print(f"   • Função de perda: Cross-entropy (mlogloss)")

    modelo = xgb.XGBClassifier(**model_params)
    modelo.fit(X_train_scaled, y_train)

    # Fazer predições
    y_pred = modelo.predict(X_test_scaled)
    y_pred_proba = modelo.predict_proba(X_test_scaled)

    # Calcular métricas
    accuracy = accuracy_score(y_test, y_pred)

    print(f"   • Acurácia geral: {accuracy:.3f}")
    print(f"   • Relatório de classificação:")
    print(classification_report(y_test, y_pred, target_names=['Sem Ação', 'Compra', 'Venda']))

    # Separar predições por classe para compatibilidade
    y_pred_compra = (y_pred == 1).astype(int)
    y_pred_venda = (y_pred == 2).astype(int)
    y_test_compra = (y_test == 1).astype(int)
    y_test_venda = (y_test == 2).astype(int)

    resultados = {
        'modelo': modelo,
        'scaler': scaler,
        'accuracy': accuracy,
        'y_test': y_test,
        'y_pred': y_pred,
        'y_pred_proba': y_pred_proba,
        'feature_importance': modelo.feature_importances_,
        # Para compatibilidade com código existente
        'compra': {
            'modelo': modelo,
            'scaler': scaler,
            'accuracy': accuracy_score(y_test_compra, y_pred_compra),
            'y_test': y_test_compra,
            'y_pred': y_pred_compra,
            'feature_importance': modelo.feature_importances_
        },
        'venda': {
            'modelo': modelo,
            'scaler': scaler,
            'accuracy': accuracy_score(y_test_venda, y_pred_venda),
            'y_test': y_test_venda,
            'y_pred': y_pred_venda,
            'feature_importance': modelo.feature_importances_
        }
    }

    return resultados, feature_cols

def aplicar_predicoes_modelo_multiclasse(acoes_dados, resultados_modelo, feature_cols):
    """
    Aplica as predições do modelo XGBoost multiclasse aos dados de cada ação
    """
    print(f"\n🔮 Aplicando predições do modelo multiclasse aos dados...")

    modelo = resultados_modelo['modelo']
    scaler = resultados_modelo['scaler']

    acoes_com_predicoes = {}

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 0:
            dados_copy = dados.copy()

            # Preparar features para predição
            try:
                # Verificar se todas as features existem
                features_disponiveis = [col for col in feature_cols if col in dados_copy.columns]
                if len(features_disponiveis) != len(feature_cols):
                    print(f"     ⚠️ Features faltando para {ticker}: {len(features_disponiveis)}/{len(feature_cols)}")
                    continue

                X = dados_copy[feature_cols].dropna()
                if len(X) == 0:
                    continue

                # Aplicar scaler se necessário
                if scaler is not None:
                    X_scaled = scaler.transform(X)
                    X_scaled = pd.DataFrame(X_scaled, columns=feature_cols, index=X.index)
                else:
                    X_scaled = X

                # Fazer predições multiclasse
                pred_multiclass = modelo.predict(X_scaled)
                pred_proba = modelo.predict_proba(X_scaled)

                # Converter predições multiclasse para formato binário compatível
                pred_compra = (pred_multiclass == 1).astype(int)
                pred_venda = (pred_multiclass == 2).astype(int)

                # Adicionar predições aos dados
                dados_copy.loc[X.index, 'Pred_Multiclass'] = pred_multiclass
                dados_copy.loc[X.index, 'Pred_Compra'] = pred_compra
                dados_copy.loc[X.index, 'Pred_Venda'] = pred_venda

                # Adicionar probabilidades
                dados_copy.loc[X.index, 'Prob_Sem_Acao'] = pred_proba[:, 0]
                dados_copy.loc[X.index, 'Prob_Compra'] = pred_proba[:, 1]
                dados_copy.loc[X.index, 'Prob_Venda'] = pred_proba[:, 2]

                # Preencher NaN com valores padrão
                dados_copy['Pred_Multiclass'] = dados_copy['Pred_Multiclass'].fillna(0).astype(int)
                dados_copy['Pred_Compra'] = dados_copy['Pred_Compra'].fillna(0).astype(int)
                dados_copy['Pred_Venda'] = dados_copy['Pred_Venda'].fillna(0).astype(int)
                dados_copy['Prob_Sem_Acao'] = dados_copy['Prob_Sem_Acao'].fillna(1.0)
                dados_copy['Prob_Compra'] = dados_copy['Prob_Compra'].fillna(0.0)
                dados_copy['Prob_Venda'] = dados_copy['Prob_Venda'].fillna(0.0)

                acoes_com_predicoes[ticker] = dados_copy

            except Exception as e:
                print(f"     ❌ Erro ao aplicar predições para {ticker}: {e}")
                acoes_com_predicoes[ticker] = dados_copy

    print(f"   ✅ Predições aplicadas a {len(acoes_com_predicoes)} ações")
    return acoes_com_predicoes

def criar_graficos_estruturados(resultados, feature_cols, acoes_dados):
    """
    Cria gráficos organizados seguindo estrutura MM/Butterworth
    """
    print(f"\n📊 Criando gráficos estruturados...")

    # Criar diretórios
    figures_dir = 'results/figures/xgboost_analysis'
    os.makedirs(figures_dir, exist_ok=True)

    # 1. Gráfico principal dos resultados do modelo
    criar_grafico_principal_modelo(resultados, feature_cols, figures_dir)

    # 2. Gráficos individuais por ação (amostra)
    criar_graficos_individuais_acoes(acoes_dados, figures_dir)

    print(f"   ✅ Gráficos salvos em: {figures_dir}/")

def criar_grafico_principal_modelo(resultados, feature_cols, figures_dir):
    """
    Cria o gráfico principal com resultados do modelo multiclasse
    """
    # Configurar estilo
    plt.style.use('default')

    # Criar figura com subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Classificador XGBoost Multiclasse - Sinais de Trading', fontsize=16, fontweight='bold')

    # Matriz de confusão - Modelo multiclasse
    cm_multiclass = confusion_matrix(resultados['y_test'], resultados['y_pred'])
    sns.heatmap(cm_multiclass, annot=True, fmt='d', cmap='viridis', ax=axes[0,0])
    axes[0,0].set_title(f'Matriz de Confusão - Modelo Multiclasse\nAcurácia: {resultados["accuracy"]:.3f}')
    axes[0,0].set_xlabel('Predito')
    axes[0,0].set_ylabel('Real')
    axes[0,0].set_xticklabels(['Sem Ação', 'Compra', 'Venda'])
    axes[0,0].set_yticklabels(['Sem Ação', 'Compra', 'Venda'])

    # Matriz de confusão - Sinais de Compra (binário)
    cm_compra = confusion_matrix(resultados['compra']['y_test'], resultados['compra']['y_pred'])
    sns.heatmap(cm_compra, annot=True, fmt='d', cmap='Blues', ax=axes[0,1])
    axes[0,1].set_title(f'Matriz de Confusão - Sinais de Compra\nAcurácia: {resultados["compra"]["accuracy"]:.3f}')
    axes[0,1].set_xlabel('Predito')
    axes[0,1].set_ylabel('Real')
    axes[0,1].set_xticklabels(['Não Comprar', 'Comprar'])
    axes[0,1].set_yticklabels(['Não Comprar', 'Comprar'])

    # Feature importance
    importance_df = pd.DataFrame({
        'feature': feature_cols,
        'importance': resultados['feature_importance']
    }).sort_values('importance', ascending=True).tail(10)

    axes[1,0].barh(importance_df['feature'], importance_df['importance'], color='lightgreen')
    axes[1,0].set_title('Top 10 Features - Modelo Multiclasse')
    axes[1,0].set_xlabel('Importância')

    # Distribuição das classes preditas
    unique, counts = np.unique(resultados['y_pred'], return_counts=True)
    class_names = ['Sem Ação', 'Compra', 'Venda']
    colors = ['gray', 'green', 'red']

    bars = axes[1,1].bar([class_names[i] for i in unique], counts, color=[colors[i] for i in unique])
    axes[1,1].set_title('Distribuição das Predições')
    axes[1,1].set_ylabel('Quantidade')

    # Adicionar valores nas barras
    for bar, count in zip(bars, counts):
        axes[1,1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + count*0.01,
                      f'{count}\n({count/len(resultados["y_pred"])*100:.1f}%)',
                      ha='center', va='bottom')

    plt.tight_layout()

    # Salvar gráfico principal
    nome_arquivo = os.path.join(figures_dir, 'xgboost_modelo_resultados.png')
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()

def criar_graficos_individuais_acoes(acoes_dados, figures_dir):
    """
    Cria gráficos individuais para TODAS as ações analisadas
    """
    print(f"   📊 Criando gráficos individuais para {len(acoes_dados)} ações...")

    # Criar subdiretório para gráficos individuais
    individual_dir = os.path.join(figures_dir, 'individual_stocks')
    os.makedirs(individual_dir, exist_ok=True)

    # Carregar informações da carteira
    try:
        acoes_carteira = carregar_acoes_carteira()
        carteira_info = {ticker: (nome, qtd) for ticker, nome, qtd in acoes_carteira}
    except:
        carteira_info = {}

    graficos_criados = 0

    for ticker in acoes_dados.keys():
        dados = acoes_dados[ticker]
        if dados is not None and len(dados) > 50:  # Mínimo de 50 dias de dados
            try:
                criar_grafico_individual_acao(ticker, dados, individual_dir, carteira_info)
                graficos_criados += 1
            except Exception as e:
                print(f"     ⚠️ Erro ao criar gráfico para {ticker}: {e}")

    print(f"   ✅ {graficos_criados} gráficos individuais criados em: {individual_dir}/")

def criar_grafico_individual_acao(ticker, dados, figures_dir, carteira_info):
    """
    Cria gráfico individual para uma ação com informações da carteira
    """
    ticker_clean = ticker.replace('.SA', '')

    # Verificar se está na carteira
    quantidade_carteira = carteira_info.get(ticker, (None, 0))[1]
    na_carteira = quantidade_carteira > 0

    # Configurar figura com 3 subplots
    fig, axes = plt.subplots(3, 1, figsize=(14, 12))

    # Título do gráfico - incluir informação da carteira
    if na_carteira:
        titulo_base = f'XGBoost - {ticker_clean} - {quantidade_carteira:.0f} ações na carteira'
    else:
        titulo_base = f'XGBoost - {ticker_clean} - SEM POSIÇÃO'

    fig.suptitle(titulo_base, fontsize=16, fontweight='bold')

    # Últimos 12 meses para visualização (ou todos os dados se menos)
    dados_recentes = dados.tail(min(252, len(dados)))  # 252 dias úteis ≈ 1 ano

    # Gráfico 1: Preço e Sinais de Trading
    ax1 = axes[0]
    ax1.plot(dados_recentes.index, dados_recentes['Media_OHLC'],
             label='Média OHLC', color='blue', linewidth=2)

    # Marcar sinais de compra (usar predições se disponíveis, senão usar sinais de treinamento)
    coluna_compra = 'Pred_Compra' if 'Pred_Compra' in dados_recentes.columns else 'Sinal_Compra'
    coluna_venda = 'Pred_Venda' if 'Pred_Venda' in dados_recentes.columns else 'Sinal_Venda'

    sinais_compra = dados_recentes[dados_recentes[coluna_compra] == 1]
    if len(sinais_compra) > 0:
        label_compra = f'Predição Compra ({len(sinais_compra)})' if 'Pred_Compra' in dados_recentes.columns else f'Sinal Compra ({len(sinais_compra)})'
        ax1.scatter(sinais_compra.index, sinais_compra['Media_OHLC'],
                   color='green', marker='^', s=80, label=label_compra,
                   alpha=0.8, edgecolors='darkgreen', linewidth=1)

    # Marcar sinais de venda (usar predições se disponíveis)
    sinais_venda = dados_recentes[dados_recentes[coluna_venda] == 1]
    if len(sinais_venda) > 0:
        label_venda = f'Predição Venda ({len(sinais_venda)})' if 'Pred_Venda' in dados_recentes.columns else f'Sinal Venda ({len(sinais_venda)})'
        ax1.scatter(sinais_venda.index, sinais_venda['Media_OHLC'],
                   color='red', marker='v', s=80, label=label_venda,
                   alpha=0.8, edgecolors='darkred', linewidth=1)

    # Destacar último sinal se houver (usar predições)
    ultimo_dia = dados_recentes.iloc[-1]
    if ultimo_dia[coluna_compra] == 1:
        label_hoje = '🟢 MODELO PREDIZ: COMPRAR HOJE' if 'Pred_Compra' in dados_recentes.columns else '🟢 COMPRAR HOJE'
        ax1.scatter(ultimo_dia.name, ultimo_dia['Media_OHLC'],
                   color='lime', marker='^', s=150, label=label_hoje,
                   alpha=1.0, edgecolors='darkgreen', linewidth=2)
    elif ultimo_dia[coluna_venda] == 1:
        label_hoje = '🔴 MODELO PREDIZ: VENDER HOJE' if 'Pred_Venda' in dados_recentes.columns else '🔴 VENDER HOJE'
        ax1.scatter(ultimo_dia.name, ultimo_dia['Media_OHLC'],
                   color='orangered', marker='v', s=150, label=label_hoje,
                   alpha=1.0, edgecolors='darkred', linewidth=2)

    ax1.set_title('Preço e Sinais de Trading XGBoost', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Preço (R$)', fontsize=11)
    ax1.legend(loc='upper left', fontsize=10)
    ax1.grid(True, alpha=0.3)

    # Gráfico 2: Volume
    ax2 = axes[1]
    ax2.bar(dados_recentes.index, dados_recentes['Volume'],
            alpha=0.6, color='steelblue', label='Volume')

    # Destacar volume nos dias de sinais
    if len(sinais_compra) > 0:
        ax2.bar(sinais_compra.index, sinais_compra['Volume'],
                alpha=0.8, color='green', label='Volume - Compra')
    if len(sinais_venda) > 0:
        ax2.bar(sinais_venda.index, sinais_venda['Volume'],
                alpha=0.8, color='red', label='Volume - Venda')

    ax2.set_title('Volume de Negociação', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Volume', fontsize=11)
    ax2.legend(loc='upper left', fontsize=10)
    ax2.grid(True, alpha=0.3)

    # Gráfico 3: Volatilidade e Spread
    ax3 = axes[2]
    ax3_twin = ax3.twinx()

    # Volatilidade
    ax3.plot(dados_recentes.index, dados_recentes['Volatilidade'],
             color='orange', linewidth=2, label='Volatilidade', alpha=0.8)

    # Spread (se disponível)
    if 'Spread' in dados_recentes.columns:
        ax3_twin.plot(dados_recentes.index, dados_recentes['Spread'],
                     color='purple', linewidth=2, label='Spread', alpha=0.8)
        ax3_twin.set_ylabel('Spread', fontsize=11, color='purple')
        ax3_twin.legend(loc='upper right', fontsize=10)

    ax3.set_title('Volatilidade e Spread', fontsize=12, fontweight='bold')
    ax3.set_ylabel('Volatilidade', fontsize=11, color='orange')
    ax3.set_xlabel('Data', fontsize=11)
    ax3.legend(loc='upper left', fontsize=10)
    ax3.grid(True, alpha=0.3)

    # Ajustar layout
    plt.tight_layout()

    # Salvar gráfico individual
    nome_arquivo = os.path.join(figures_dir, f'xgboost_{ticker_clean}.png')
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()

def salvar_modelos_estruturado(resultados, feature_cols):
    """
    Salva o modelo multiclasse treinado em estrutura organizada
    """
    print(f"\n💾 Salvando modelo multiclasse...")

    # Criar diretório para modelos XGBoost
    modelo_dir = 'results/models/xgboost_analysis'
    os.makedirs(modelo_dir, exist_ok=True)

    # Remover modelos antigos se existirem
    old_files = ['modelo_sinais_compra.pkl', 'modelo_sinais_venda.pkl']
    for old_file in old_files:
        old_path = os.path.join(modelo_dir, old_file)
        if os.path.exists(old_path):
            os.remove(old_path)
            print(f"   🗑️ Removido modelo antigo: {old_file}")

    # Salvar modelo multiclasse principal
    modelo_multiclass_path = os.path.join(modelo_dir, 'modelo_multiclasse.pkl')
    with open(modelo_multiclass_path, 'wb') as f:
        pickle.dump({
            'modelo': resultados['modelo'],
            'scaler': resultados['scaler'],
            'feature_cols': feature_cols,
            'accuracy': resultados['accuracy'],
            'classes': ['Sem Ação', 'Compra', 'Venda'],
            'config_usado': {
                'data_period': config.get('xgboost.data_period'),
                'signal_horizon': config.get('xgboost.signal_horizon'),
                'ohlc_lags': config.get('xgboost.features.ohlc_lags'),
                'model_params': config.get('xgboost.model_params')
            }
        }, f)

    # Salvar resumo detalhado dos resultados
    resumo_path = os.path.join(modelo_dir, 'resumo_treinamento.txt')
    with open(resumo_path, 'w') as f:
        f.write("RESUMO DO TREINAMENTO XGBOOST MULTICLASSE - SINAIS DE TRADING\n")
        f.write("=" * 70 + "\n\n")
        f.write(f"Data de treinamento: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        f.write("MODELO MULTICLASSE:\n")
        f.write("  • Tipo: XGBoost Multiclasse\n")
        f.write("  • Classes: 0=Sem Ação, 1=Compra, 2=Venda\n")
        f.write("  • Função de perda: Cross-entropy (mlogloss)\n")
        f.write(f"  • Acurácia geral: {resultados['accuracy']:.3f}\n\n")

        f.write("CONFIGURAÇÕES UTILIZADAS:\n")
        f.write(f"  • Período de dados: {config.get('xgboost.data_period')}\n")
        f.write(f"  • Horizonte de sinais: {config.get('xgboost.signal_horizon')} dias\n")
        f.write(f"  • Lags OHLC: {config.get('xgboost.features.ohlc_lags')}\n")
        f.write(f"  • Janela volatilidade: {config.get('xgboost.features.volatility_window')}\n")
        f.write(f"  • Multiplicador spread: {config.get('xgboost.features.spread_multiplier')}\n\n")

        f.write(f"FEATURES UTILIZADAS ({len(feature_cols)}):\n")
        for i, feature in enumerate(feature_cols, 1):
            f.write(f"  {i:2d}. {feature}\n")

        f.write(f"\nRESULTADOS POR CLASSE:\n")
        f.write(f"  • Acurácia Geral: {resultados['accuracy']:.3f}\n")
        f.write(f"  • Acurácia Sinais de Compra (binário): {resultados['compra']['accuracy']:.3f}\n")
        f.write(f"  • Acurácia Sinais de Venda (binário): {resultados['venda']['accuracy']:.3f}\n\n")

        f.write(f"DEFINIÇÃO DOS SINAIS:\n")
        signal_horizon = config.get('xgboost.signal_horizon')
        f.write(f"  • Sinal de Compra: Média OHLC atual < Média OHLC {signal_horizon} dias à frente\n")
        f.write(f"  • Sinal de Venda: Média OHLC atual > Média OHLC {signal_horizon} dias à frente\n")
        f.write(f"  • Sem Ação: Casos onde não há sinal de compra nem venda\n\n")

        f.write(f"PARÂMETROS DO XGBOOST:\n")
        model_params = config.get('xgboost.model_params')
        for param, valor in model_params.items():
            f.write(f"  • {param}: {valor}\n")
        f.write(f"  • objective: multi:softprob (adicionado automaticamente)\n")
        f.write(f"  • num_class: 3 (adicionado automaticamente)\n")
        f.write(f"  • eval_metric: mlogloss (adicionado automaticamente)\n")

    print(f"   ✅ Modelo multiclasse salvo em: {modelo_dir}/")
    print(f"   📄 Resumo: {resumo_path}")

    return modelo_dir

def salvar_dados_csv_estruturado(acoes_dados, resultados_modelo=None):
    """
    Salva dados em estrutura organizada seguindo padrão MM/Butterworth
    Inclui predições do modelo se disponíveis
    """
    print(f"\n💾 Salvando dados em estrutura organizada...")

    # Criar diretórios
    csv_dir = 'results/csv/xgboost_analysis'
    individual_dir = os.path.join(csv_dir, 'individual_stocks')
    os.makedirs(csv_dir, exist_ok=True)
    os.makedirs(individual_dir, exist_ok=True)

    # Lista para dados completos
    dados_completos = []

    # Processar cada ação individualmente
    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 0:
            # Preparar dados para CSV
            dados_csv = dados.copy()
            ticker_clean = ticker.replace('.SA', '')
            dados_csv['Ticker'] = ticker_clean

            # Selecionar colunas relevantes (incluindo predições se disponíveis)
            colunas_csv = [
                'Ticker', 'Media_OHLC', 'Volume', 'Spread', 'Volatilidade',
                'Sinal_Compra', 'Sinal_Venda', 'Media_OHLC_Futura'
            ]

            # Adicionar colunas de predição se existirem
            if 'Pred_Compra' in dados_csv.columns:
                colunas_csv.append('Pred_Compra')
            if 'Pred_Venda' in dados_csv.columns:
                colunas_csv.append('Pred_Venda')

            # Verificar se todas as colunas existem
            colunas_existentes = [col for col in colunas_csv if col in dados_csv.columns]

            if len(colunas_existentes) >= 6:  # Pelo menos as colunas principais
                dados_selecionados = dados_csv[colunas_existentes].copy()
                dados_selecionados.reset_index(inplace=True)
                dados_selecionados['Data'] = dados_selecionados['Date'].dt.strftime('%Y-%m-%d')
                dados_selecionados.drop('Date', axis=1, inplace=True)

                # Reordenar colunas
                cols_ordenadas = ['Ticker', 'Data'] + [col for col in colunas_existentes if col != 'Ticker']
                dados_selecionados = dados_selecionados[cols_ordenadas]

                # Salvar arquivo individual
                arquivo_individual = os.path.join(individual_dir, f'xgboost_{ticker_clean}.csv')
                dados_selecionados.to_csv(arquivo_individual, index=False)

                dados_completos.append(dados_selecionados)

    if dados_completos:
        # Combinar todos os dados
        df_final = pd.concat(dados_completos, ignore_index=True)
        df_final = df_final.sort_values(['Ticker', 'Data'])

        # Salvar arquivo completo principal
        csv_completo_path = os.path.join(csv_dir, 'resultados_xgboost_completo.csv')
        df_final.to_csv(csv_completo_path, index=False)

        # Criar resumo por ação
        resumo_por_acao = df_final.groupby('Ticker').agg({
            'Sinal_Compra': 'sum',
            'Sinal_Venda': 'sum',
            'Media_OHLC': ['first', 'last', 'mean'],
            'Volume': 'mean',
            'Volatilidade': 'mean',
            'Data': 'count'
        }).round(2)

        # Flatten column names
        resumo_por_acao.columns = ['Sinais_Compra', 'Sinais_Venda', 'Preco_Inicial', 'Preco_Final', 'Preco_Medio', 'Volume_Medio', 'Volatilidade_Media', 'Total_Dias']
        resumo_por_acao['Performance_%'] = ((resumo_por_acao['Preco_Final'] / resumo_por_acao['Preco_Inicial']) - 1) * 100
        resumo_por_acao['Total_Sinais'] = resumo_por_acao['Sinais_Compra'] + resumo_por_acao['Sinais_Venda']

        # Salvar resumo
        resumo_path = os.path.join(csv_dir, 'resultados_xgboost.csv')
        resumo_por_acao.to_csv(resumo_path)

        print(f"   ✅ Dados salvos em: {csv_dir}/")
        print(f"   📊 Total de registros: {len(df_final):,}")
        print(f"   📈 Ações processadas: {df_final['Ticker'].nunique()}")
        print(f"   📁 Arquivos individuais: {individual_dir}/")

        # Estatísticas dos sinais
        total_compra = df_final['Sinal_Compra'].sum()
        total_venda = df_final['Sinal_Venda'].sum()
        total_registros = len(df_final)

        print(f"   🟢 Sinais de Compra: {total_compra:,} ({total_compra/total_registros*100:.1f}%)")
        print(f"   🔴 Sinais de Venda: {total_venda:,} ({total_venda/total_registros*100:.1f}%)")

        return csv_completo_path
    else:
        print(f"   ❌ Nenhum dado válido para salvar")
        return None

def carregar_acoes_carteira():
    """
    Carrega as ações da carteira do arquivo CSV usando configuração
    Filtra apenas ações com quantidade líquida > 0 (ainda na carteira)
    """
    try:
        # Usar configuração ao invés de hardcode
        file_paths = config.get_file_paths()
        csv_path = file_paths.get('carteira', 'carteira.csv')
        df = pd.read_csv(csv_path)

        # Pegar todas as ações da carteira e calcular quantidade líquida
        acoes = []
        tickers_processados = set()

        for _, row in df.iterrows():
            if pd.notna(row['ticker']) and row['ticker'].strip():
                ticker = row['ticker'].strip()

                # Evitar duplicatas
                if ticker in tickers_processados:
                    continue
                tickers_processados.add(ticker)

                # Calcular quantidade líquida (compras - vendas)
                quantidade_liquida = obter_quantidade_carteira(ticker)

                # Incluir apenas ações com quantidade > 0 (ainda na carteira)
                if quantidade_liquida > 0:
                    # Extrair nome da empresa do ticker (simplificado)
                    nome = ticker.replace('.SA', '')
                    acoes.append((ticker, nome, quantidade_liquida))

        return acoes

    except Exception as e:
        print(f"❌ Erro ao carregar arquivo da carteira: {e}")
        return []

def obter_quantidade_carteira(ticker):
    """
    Obtém a quantidade líquida de ações de um ticker na carteira usando configuração
    Valores negativos na quantidade indicam vendas
    """
    try:
        # Usar configuração ao invés de hardcode
        file_paths = config.get_file_paths()
        csv_path = file_paths.get('carteira', 'carteira.csv')
        df = pd.read_csv(csv_path)

        # Filtrar por ticker e somar quantidades (compras - vendas)
        ticker_data = df[df['ticker'] == ticker]
        if not ticker_data.empty:
            quantidade_liquida = ticker_data['quantidade'].sum()
            return quantidade_liquida
        else:
            return 0

    except Exception as e:
        print(f"❌ Erro ao obter quantidade da carteira para {ticker}: {e}")
        return 0

def imprimir_recomendacoes_ultimo_dia(acoes_dados):
    """
    Imprime recomendações de compra e venda para o último dia de dados
    Similar ao formato do análise Butterworth
    """
    print(f"\n📊 ESTRATÉGIA DE TRADING - CLASSIFICADOR XGBOOST")
    print(f"=" * 60)
    print(f"🎯 Sinais detectados para o último dia disponível:")

    # Carregar informações da carteira
    try:
        acoes_carteira = carregar_acoes_carteira()
        tickers_carteira = {ticker for ticker, _, _ in acoes_carteira}
        carteira_info = {ticker: (nome, qtd) for ticker, nome, qtd in acoes_carteira}
    except:
        tickers_carteira = set()
        carteira_info = {}

    sinais_compra = []
    sinais_venda = []

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 0:
            # Usar predições se disponíveis, senão usar sinais de treinamento
            if 'Pred_Compra' in dados.columns and 'Pred_Venda' in dados.columns:
                colunas_necessarias = ['Pred_Compra', 'Pred_Venda']
                coluna_compra = 'Pred_Compra'
                coluna_venda = 'Pred_Venda'
                tipo_sinal = "PREDIÇÃO"
            else:
                colunas_necessarias = ['Sinal_Compra', 'Sinal_Venda']
                coluna_compra = 'Sinal_Compra'
                coluna_venda = 'Sinal_Venda'
                tipo_sinal = "SINAL"

            # Pegar o último dia com dados válidos (não NaN)
            dados_validos = dados.dropna(subset=colunas_necessarias)

            if len(dados_validos) > 0:
                ultimo_dia = dados_validos.iloc[-1]
                ticker_clean = ticker.replace('.SA', '')
                nome_empresa = ticker_clean  # Nome simplificado

                # Verificar sinais de compra (usar predições se disponíveis)
                if ultimo_dia[coluna_compra] == 1:
                    sinais_compra.append({
                        'ticker': ticker,
                        'ticker_clean': ticker_clean,
                        'nome': nome_empresa,
                        'preco': ultimo_dia['Media_OHLC'],
                        'volume': ultimo_dia.get('Volume', 0),
                        'volatilidade': ultimo_dia.get('Volatilidade', 0),
                        'spread': ultimo_dia.get('Spread', 0),
                        'data': ultimo_dia.name,
                        'na_carteira': ticker in tickers_carteira,
                        'quantidade_carteira': carteira_info.get(ticker, (None, 0))[1]
                    })

                # Verificar sinais de venda (APENAS para ações da carteira na exibição)
                if ultimo_dia[coluna_venda] == 1 and ticker in tickers_carteira:
                    sinais_venda.append({
                        'ticker': ticker,
                        'ticker_clean': ticker_clean,
                        'nome': nome_empresa,
                        'preco': ultimo_dia['Media_OHLC'],
                        'volume': ultimo_dia.get('Volume', 0),
                        'volatilidade': ultimo_dia.get('Volatilidade', 0),
                        'spread': ultimo_dia.get('Spread', 0),
                        'data': ultimo_dia.name,
                        'na_carteira': ticker in tickers_carteira,
                        'quantidade_carteira': carteira_info.get(ticker, (None, 0))[1]
                    })

    # Verificar se há sinais
    if not sinais_compra and not sinais_venda:
        print("✅ Nenhum sinal de compra ou venda detectado para o último dia.")
        return

    # Exibir sinais de compra
    if sinais_compra:
        print(f"\n🟢 SINAIS DE COMPRA ({len(sinais_compra)} ações):")
        print("-" * 60)
        for sinal in sorted(sinais_compra, key=lambda x: x['ticker_clean']):
            data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y') if pd.notna(sinal['data']) else 'N/A'
            print(f"   📈 {sinal['ticker_clean']} ({sinal['nome'][:30]})")
            print(f"      💰 Preço: R$ {sinal['preco']:.2f}")
            print(f"      📅 Data: {data_str}")
            print(f"      📊 Volume: {sinal['volume']:,.0f}")
            if sinal['volatilidade'] > 0:
                print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
            if sinal['spread'] > 0:
                print(f"      💹 Spread: {sinal['spread']:.4f}")
            print(f"      🎯 Sinal: XGBoost prevê alta no preço")
            if sinal['na_carteira']:
                print(f"      ✅ VOCÊ JÁ POSSUI: {sinal['quantidade_carteira']:.0f} ações")
            print()

    # Exibir sinais de venda
    if sinais_venda:
        print(f"\n🔴 SINAIS DE VENDA ({len(sinais_venda)} ações):")
        print("-" * 60)

        # Separar ações da carteira das demais
        vendas_carteira = [s for s in sinais_venda if s['na_carteira']]
        vendas_outras = [s for s in sinais_venda if not s['na_carteira']]

        if vendas_carteira:
            print("   🎯 AÇÕES DA SUA CARTEIRA:")
            for sinal in sorted(vendas_carteira, key=lambda x: x['ticker_clean']):
                data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y') if pd.notna(sinal['data']) else 'N/A'
                print(f"   📉 {sinal['ticker_clean']} ({sinal['nome'][:30]}) ⚠️ VOCÊ POSSUI")
                print(f"      💰 Preço: R$ {sinal['preco']:.2f}")
                print(f"      📅 Data: {data_str}")
                print(f"      📊 Volume: {sinal['volume']:,.0f}")
                if sinal['volatilidade'] > 0:
                    print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
                if sinal['spread'] > 0:
                    print(f"      💹 Spread: {sinal['spread']:.4f}")
                print(f"      🎯 Sinal: XGBoost prevê queda no preço")
                print(f"      🏦 Quantidade na carteira: {sinal['quantidade_carteira']:.0f} ações")
                print()

        if vendas_outras:
            print("   📊 OUTRAS AÇÕES:")
            for sinal in sorted(vendas_outras, key=lambda x: x['ticker_clean']):
                data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y') if pd.notna(sinal['data']) else 'N/A'
                print(f"   📉 {sinal['ticker_clean']} ({sinal['nome'][:30]})")
                print(f"      💰 Preço: R$ {sinal['preco']:.2f}")
                print(f"      📅 Data: {data_str}")
                print(f"      📊 Volume: {sinal['volume']:,.0f}")
                if sinal['volatilidade'] > 0:
                    print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
                if sinal['spread'] > 0:
                    print(f"      💹 Spread: {sinal['spread']:.4f}")
                print(f"      🎯 Sinal: XGBoost prevê queda no preço")
                print()

    # Resumo final
    print("📋 RESUMO DOS SINAIS:")
    print(f"   🟢 Compra: {len(sinais_compra)} ações")
    print(f"   🔴 Venda: {len(sinais_venda)} ações")
    if sinais_venda:
        vendas_carteira = [s for s in sinais_venda if s['na_carteira']]
        if vendas_carteira:
            print(f"   ⚠️  Vendas na sua carteira: {len(vendas_carteira)} ações")
    print(f"   📊 Total de sinais: {len(sinais_compra) + len(sinais_venda)} ações")

def main():
    """
    Função principal
    """
    # Configurar ambiente
    setup_environment()
    
    print("🤖 CLASSIFICADOR XGBOOST - SINAIS DE TRADING")
    print("=" * 80)
    print("📊 Baseado na média OHLC das ações diversificadas")

    # Mostrar configurações
    signal_horizon = config.get('xgboost.signal_horizon')
    ohlc_lags = config.get('xgboost.features.ohlc_lags')
    data_period = config.get('xgboost.data_period')

    print(f"🎯 Sinais: Compra/Venda baseados em {signal_horizon} dias à frente")
    print(f"🔧 Features: Média OHLC passada ({ohlc_lags} dias), Volume, Spread, Volatilidade")
    print(f"📅 Período de dados: {data_period}")
    print("=" * 80)
    
    # Carregar listas de ações
    acoes_diversificadas = carregar_acoes_diversificadas()
    acoes_carteira = carregar_acoes_carteira()

    # Combinar todas as ações, evitando duplicatas
    todas_acoes = []
    tickers_vistos = set()

    # Adicionar ações diversificadas
    for ticker, nome in acoes_diversificadas:
        if ticker not in tickers_vistos:
            todas_acoes.append((ticker, nome, "Diversificada"))
            tickers_vistos.add(ticker)

    # Adicionar ações da carteira
    for ticker, nome, quantidade in acoes_carteira:
        if ticker not in tickers_vistos:
            todas_acoes.append((ticker, nome, "Carteira"))
            tickers_vistos.add(ticker)
        else:
            # Marcar como ambas se já existe
            for i, (t, n, origem) in enumerate(todas_acoes):
                if t == ticker:
                    todas_acoes[i] = (t, n, "Diversificada + Carteira")
                    break

    if not todas_acoes:
        print("❌ Não foi possível carregar ações dos arquivos CSV")
        return

    print(f"\n📋 Serão analisadas {len(todas_acoes)} ações no total:")
    print(f"   • Ações diversificadas: {len(acoes_diversificadas)}")
    print(f"   • Ações da carteira: {len(acoes_carteira)}")
    print(f"   • Total único: {len(todas_acoes)}")

    # Criar set de tickers da carteira para verificação de sinais de venda
    tickers_carteira = {ticker for ticker, _, _ in acoes_carteira}
    
    # Baixar dados e calcular features
    print(f"\n📥 Baixando dados de {len(todas_acoes)} ações...")
    acoes_dados = {}

    for ticker, nome, origem in todas_acoes:
        dados = baixar_dados_acao(ticker, nome)
        if dados is not None:
            dados_processados = calcular_features_e_sinais(dados, ticker, tickers_carteira)
            acoes_dados[ticker] = dados_processados
    
    print(f"✅ Processadas {len(acoes_dados)} ações com sucesso")

    # Salvar sinais em CSV
    salvar_dados_csv_estruturado(acoes_dados)

    # Preparar dataset
    print(f"\n🔧 Preparando dataset...")
    X, y_multiclass, feature_cols, dataset_completo = preparar_dataset(acoes_dados)

    if X is None:
        print("❌ Erro ao preparar dataset")
        return

    # Treinar classificador multiclasse com divisão temporal
    resultados, feature_cols = treinar_classificador_multiclasse(X, y_multiclass, feature_cols, dataset_completo)

    if resultados is None:
        print("❌ Erro no treinamento do classificador")
        return

    # Aplicar predições do modelo aos dados
    acoes_com_predicoes = aplicar_predicoes_modelo_multiclasse(acoes_dados, resultados, feature_cols)

    # Salvar dados com predições em CSV
    print(f"\n💾 Salvando dados com predições...")
    salvar_dados_csv_estruturado(acoes_com_predicoes)

    # Criar gráficos usando predições
    print(f"\n📊 Criando gráficos com predições do modelo...")
    criar_graficos_estruturados(resultados, feature_cols, acoes_com_predicoes)

    # Salvar modelos
    print(f"\n💾 Salvando modelos...")
    salvar_modelos_estruturado(resultados, feature_cols)

    # Imprimir recomendações para o último dia usando predições
    imprimir_recomendacoes_ultimo_dia(acoes_com_predicoes)

    print(f"\n✅ Análise concluída!")
    print(f"📊 Resultados:")
    print(f"   • Acurácia Sinais de Compra: {resultados['compra']['accuracy']:.3f}")
    print(f"   • Acurácia Sinais de Venda: {resultados['venda']['accuracy']:.3f}")

if __name__ == "__main__":
    main()
